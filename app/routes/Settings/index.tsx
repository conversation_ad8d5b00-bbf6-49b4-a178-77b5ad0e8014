import React from "react";
import Layout from "../../components/layout/Layout";
import SettingsCard from "../../components/settings/SettingsCard";

export default function SettingsPage() {
  const settingsCards = [
    {
      title: "Language",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "🌐",
    },
    {
      title: "NLU",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "🧠",
    },
    {
      title: "LLM Configuration",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "⚙️",
    },
    {
      title: "Content Resources",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "📚",
    },
    {
      title: "Personalization",
      description:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "👤",
    },
  ];

  const headerActions = (
    <div className="flex items-center space-x-3">
      <button className="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
        🔍 PREVIEW
      </button>
      <button className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
        📤 PUBLISH
      </button>
    </div>
  );

  return (
    <Layout
      title="Untitled_25_04_25_0950"
      subtitle="No description"
      headerActions={headerActions}
    >
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {settingsCards.map((card, index) => (
            <SettingsCard
              key={index}
              title={card.title}
              description={card.description}
              icon={card.icon}
              onClick={() => console.log(`Clicked ${card.title}`)}
            />
          ))}
        </div>
      </div>
    </Layout>
  );
}
