import React from "react";
import { Link, useLocation } from "react-router-dom";

interface SidebarProps {
  collapsed?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed = false }) => {
  const location = useLocation();

  const menuItems = [
    { icon: "🏠", label: "Home", path: "/", id: "home" },
    { icon: "📊", label: "Insights", path: "/insights", id: "insights" },
    { icon: "#", label: "Assets", path: "/assets", id: "assets" },
    { icon: "🔍", label: "Reach", path: "/reach", id: "reach" },
    { icon: "📁", label: "Directory", path: "/directory", id: "directory" },
    { icon: "🎨", label: "Studio", path: "/studio", id: "studio" },
    { icon: "💬", label: "Halo", path: "/halo", id: "halo" },
    {
      icon: "🤖",
      label: "NeuraTalk AI",
      path: "/",
      id: "neuratalk",
      active: true,
    },
    { icon: "📋", label: "Plans", path: "/plans", id: "plans" },
    { icon: "📝", label: "Logs", path: "/logs", id: "logs" },
    { icon: "👥", label: "Developers", path: "/developers", id: "developers" },
  ];

  const isActive = (path: string) => {
    if (path === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(path);
  };

  return (
    <div
      className={`bg-[#2D3748] text-white h-screen flex flex-col transition-all duration-300 ${
        collapsed ? "w-16" : "w-64"
      }`}
    >
      {/* Logo */}
      <div className="p-4 border-b border-[#4A5568]">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-white rounded flex items-center justify-center">
            <span className="text-[#2D3748] font-bold text-sm">C</span>
          </div>
          {!collapsed && <span className="text-xl font-bold">comviva</span>}
        </div>
      </div>

      {/* User Profile */}
      <div className="p-4 border-b border-[#4A5568]">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-[#4A5568] rounded-full flex items-center justify-center relative">
            <span className="text-sm text-white">JD</span>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs flex items-center justify-center">
              <span className="text-white text-xs">1</span>
            </div>
          </div>
          {!collapsed && (
            <div>
              <div className="text-sm font-medium text-white">John Doe</div>
              <div className="text-xs text-gray-300">Bot $ 10K</div>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 py-4">
        <ul className="space-y-1">
          {menuItems.map((item) => (
            <li key={item.id}>
              <Link
                to={item.path}
                className={`flex items-center px-4 py-3 text-sm transition-colors duration-200 ${
                  item.active || isActive(item.path)
                    ? "bg-[#EF4444] text-white border-r-4 border-[#DC2626]"
                    : "text-gray-300 hover:bg-[#4A5568] hover:text-white"
                }`}
              >
                <span className="text-lg mr-3">{item.icon}</span>
                {!collapsed && <span>{item.label}</span>}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-[#4A5568]">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-[#4A5568] rounded flex items-center justify-center">
            <span className="text-xs text-white">C+</span>
          </div>
          {!collapsed && (
            <div>
              <div className="text-xs text-gray-300">Logout</div>
              <div className="text-xs text-gray-400">
                2023 © Comviva Technologies Limited
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
