import { importShared } from './__federation_fn_import-CJyKoOdj.js';
import { j as jsxRuntimeExports } from './jsx-runtime-CvJTHeKY.js';

const {Outlet} = await importShared('react-router-dom');

function Agents() {
  return /* @__PURE__ */ jsxRuntimeExports.jsx("main", { className: "flex items-center justify-center pt-16 pb-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 flex flex-col items-center gap-16 min-h-0", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("header", { className: "flex flex-col items-center gap-9", children: "Agents" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(Outlet, {})
  ] }) });
}

export { Agents as default };
