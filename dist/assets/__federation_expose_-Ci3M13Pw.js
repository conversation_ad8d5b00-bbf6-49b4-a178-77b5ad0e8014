import { importShared } from './__federation_fn_import-CJyKoOdj.js';
import { j as jsxRuntimeExports } from './jsx-runtime-CvJTHeKY.js';
import { g as getDefaultExportFromCjs } from './_commonjsHelpers-B85MJLTf.js';
import { r as requireReactDom } from './index-eoEhLOdg.js';
import App from './__federation_expose_App-C17X1zFY.js';
export { default as routes } from './__federation_expose_Routes-CoSTEVjW.js';
export { a as apiSlice, b as authReducer, d as selectCurrentAccessToken, f as selectCurrentAuthState, c as selectCurrentLoginStatus, e as selectCurrentRefreshToken, s as setCredentials, store, u as unsetCredentials } from './__federation_expose_Store-krOtjX4R.js';
export { default as Home, u as useAppDispatch, a as useAppSelector } from './__federation_expose_RoutesHome-hhrKxW3a.js';
export { default as RouterExample } from './__federation_expose_ComponentsRouterExample-DSDK2k_Y.js';
export { default as MicroFrontendDemo } from './__federation_expose_componentsMicroFrontendDemo-DvRTI6td.js';
export { default as Agents } from './__federation_expose_RoutesAgents-h6Zc5M1N.js';
export { default as NewAgents } from './__federation_expose_RoutesNewAgent-Dc-UYM85.js';

var client = {};

var hasRequiredClient;

function requireClient () {
	if (hasRequiredClient) return client;
	hasRequiredClient = 1;
	var m = requireReactDom();
	{
	  client.createRoot = m.createRoot;
	  client.hydrateRoot = m.hydrateRoot;
	}
	return client;
}

var clientExports = requireClient();
const ReactDOM = /*@__PURE__*/getDefaultExportFromCjs(clientExports);

await importShared('react');

function SimpleTestComponent() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { style: {
    padding: "20px",
    margin: "20px",
    border: "2px solid blue",
    borderRadius: "8px",
    backgroundColor: "#f0f8ff"
  }, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { style: { color: "#0066cc" }, children: "Simple Test Component" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "This component is loaded from the Bot UI micro-frontend." }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "If you can see this, the integration is working!" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { children: [
      "Current time: ",
      (/* @__PURE__ */ new Date()).toLocaleTimeString()
    ] })
  ] });
}

const React = await importShared('react');
const isFederated = window.location.search.includes("federated=true");
if (!isFederated) {
  const rootElement = document.getElementById("root");
  if (rootElement) {
    ReactDOM.createRoot(rootElement).render(
      /* @__PURE__ */ jsxRuntimeExports.jsx(React.StrictMode, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(App, {}) })
    );
  }
}

export { App, SimpleTestComponent };
