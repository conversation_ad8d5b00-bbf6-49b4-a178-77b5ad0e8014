import { importShared } from './__federation_fn_import-CJyKoOdj.js';
import { j as jsxRuntimeExports } from './jsx-runtime-CvJTHeKY.js';

const {useState} = await importShared('react');

function MicroFrontendDemo() {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState("Hello from Bot UI Micro-Frontend!");
  const incrementCount = () => {
    setCount(count + 1);
  };
  const changeMessage = () => {
    const messages = [
      "Hello from Bot UI Micro-Frontend!",
      "Integration successful!",
      "Module Federation is working!",
      "You can now use all components from this micro-frontend!",
      "Congratulations on setting up your micro-frontend architecture!"
    ];
    const randomIndex = Math.floor(Math.random() * messages.length);
    setMessage(messages[randomIndex]);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-6 max-w-md mx-auto bg-white rounded-xl shadow-md flex flex-col items-center space-y-4 mt-4", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-bold text-blue-600", children: "Micro-Frontend Demo" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500 mt-2", children: "This component is loaded from the Bot UI micro-frontend" })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "bg-blue-100 p-4 rounded-lg w-full text-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-lg font-semibold", children: message }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col items-center space-y-2 w-full", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-gray-700", children: [
        "Counter: ",
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-bold", children: count })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex space-x-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: incrementCount,
            className: "px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",
            children: "Increment"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: changeMessage,
            className: "px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors",
            children: "Change Message"
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "border-t border-gray-200 pt-4 w-full", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-sm text-gray-500 text-center", children: [
      "Current time: ",
      (/* @__PURE__ */ new Date()).toLocaleTimeString()
    ] }) })
  ] });
}

export { MicroFrontendDemo as default };
